'use client'

import { useDownloader } from '../../hooks/download/useDownloader';
import DownloadCard from './download/DownloadCard';
import { IconArrowRight } from '@tabler/icons-react';

function App() {
  const {
    isLoading,
    error,
    downloadData,
    downloadProgress,
    startDownloadWithData,
    saveToLocal,
    savedFiles,
    updateDownloadDataFilename,
    stopLiveRecording
  } = useDownloader();

  // 添加调试日志
  console.log('🔍 App 组件状态:', {
    isLoading,
    error,
    downloadData: !!downloadData
  });

  return (
    < >
      {isLoading && (
        <button
          disabled={true}
          className="flex flex-row justify-center items-center px-6 py-3.5 gap-2 border-none rounded-lg cursor-pointer bg-blue-700 text-white"
          style={{
            width: '136px',
            height: '52px'
          }}
        >
          <span className="font-inter font-medium text-base leading-6 text-white">
            立即开始
          </span>

          <IconArrowRight
            size={16}
            color="#FFFFFF"
          />
        </button>
      )}

      {error && (
        <div className="loading">
          <p className="text-gray-500 text-center text-xl font-normal leading-[150%] self-stretch font-inter">
            安装 SnapAny 插件，轻松保存网页视频。操作便捷，安全可靠，永久免费使用。
          </p>
        </div>
      )}

      {!isLoading && !error && (
        <>
          <DownloadCard
            downloadData={downloadData}
            downloadProgress={downloadProgress}
            startDownloadWithData={startDownloadWithData}
            saveToLocal={saveToLocal}
            savedFiles={savedFiles}
            updateDownloadDataFilename={updateDownloadDataFilename}
            stopLiveRecording={stopLiveRecording}
          />
        </>
      )}
    </>
  );
}

export default App;